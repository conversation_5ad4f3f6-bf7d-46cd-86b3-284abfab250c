<div
  class="d-flex justify-content-center align-items-center min-vh-100 bg-light"
>
  <div class="card w-100" style="max-width: 400px">
    <div class="card-body p-4">
      <h2 class="card-title text-center mb-4">Login</h2>

      <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
        <div class="mb-3">
          <label for="username" class="form-label">Username</label>
          <input
            type="text"
            id="username"
            formControlName="username"
            class="form-control"
            [ngClass]="{ 'is-invalid': submitted && f['username'].errors }"
          />
          <div
            *ngIf="submitted && f['username'].errors"
            class="invalid-feedback"
          >
            <div
              *ngIf="f['username'].errors && f['username'].errors['required']"
            >
              Username is required
            </div>
          </div>
        </div>

        <div class="mb-3">
          <label for="password" class="form-label">Password</label>
          <input
            type="password"
            id="password"
            formControlName="password"
            class="form-control"
            [ngClass]="{ 'is-invalid': submitted && f['password'].errors }"
          />
          <div
            *ngIf="submitted && f['password'].errors"
            class="invalid-feedback"
          >
            <div
              *ngIf="f['password'].errors && f['password'].errors['required']"
            >
              Password is required
            </div>
          </div>
        </div>

        <div *ngIf="error" class="alert alert-danger mb-3">
          <i class="bi bi-exclamation-triangle me-2"></i>
          <span>{{ error }}</span>
        </div>

        <div class="d-grid">
          <button type="submit" class="btn btn-primary">Login</button>
        </div>
      </form>
    </div>
  </div>
</div>
