import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ProfileService } from '../../../core/services/profile/profile.service';
import { ProfileData } from '../../../core/models/profile.model';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.css'],
  standalone: false,
})
export class ProfileComponent implements OnInit {
  profileData?: ProfileData;
  loading = true;
  error = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private profileService: ProfileService
  ) {}

  ngOnInit(): void {
    this.route.paramMap.subscribe((params) => {
      const id = params.get('id');
      if (id) {
        this.loadProfile(+id);
      } else {
        this.router.navigate(['/profile']);
      }
    });
  }

  loadProfile(id: number): void {
    this.loading = true;
    this.error = false;

    this.profileService.getProfileById(id).subscribe({
      next: (profile) => {
        if (profile) {
          this.profileData = profile;
          this.loading = false;
        } else {
          this.error = true;
          this.loading = false;
        }
      },
      error: (err) => {
        console.error('Error loading profile', err);
        this.error = true;
        this.loading = false;
      },
    });
  }
}
