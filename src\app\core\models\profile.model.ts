export interface PracticeArea {
  name: string;
  level: 'Expert' | 'Advanced' | 'Intermediate' | 'Beginner';
}

export interface Skill {
  name: string;
}

export interface IndustryExperience {
  name: string;
}

export interface Education {
  degree: string;
  institution: string;
  year?: string;
}

export interface BarAdmission {
  state: string;
  year?: string;
}

export interface NotableMatter {
  title: string;
  description: string;
  year: string;
  role: string;
}

export interface Availability {
  status: 'Available now' | 'Available next week' | 'Available next month';
  matchPercentage?: number;
}

export interface ProfileData {
  id: number;
  name: string;
  initials: string;
  title: string;
  email: string;
  phone: string;
  location: string;
  yearsExperience: number;
  yearsWithFirm?: number;
  avatarUrl?: string;
  practiceAreas: PracticeArea[];
  skills: Skill[];
  industryExperience: IndustryExperience[];
  professionalSummary: string;
  education: Education[];
  barAdmissions: BarAdmission[];
  notableMatters: NotableMatter[];
  availability: Availability;
}
