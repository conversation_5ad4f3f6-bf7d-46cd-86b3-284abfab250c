import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Component({
  selector: 'app-icon',
  template: `<span [innerHTML]="svgContent"></span>`,
  standalone: true,
  imports: [CommonModule],
})
export class IconComponent implements OnInit {
  @Input() name!: string;
  @Input() size: number = 16;

  svgContent: SafeHtml = '';

  constructor(private sanitizer: DomSanitizer) {}

  ngOnInit(): void {
    this.updateIcon();
  }

  private updateIcon(): void {
    let svg = '';

    switch (this.name) {
      case 'info-circle':
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><path d="M12 8h.01"/></svg>`;
        break;
      case 'map-pin':
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"/><circle cx="12" cy="10" r="3"/></svg>`;
        break;
      case 'briefcase':
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="14" x="2" y="7" rx="2" ry="2"/><path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"/></svg>`;
        break;
      case 'book':
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"/></svg>`;
        break;
      case 'calendar':
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M8 2v3"/><path d="M16 2v3"/><path d="M3 10h18"/><path d="M3 5h18v16H3z"/></svg>`;
        break;
      case 'chevron-down':
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>`;
        break;
      case 'chevron-left':
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m15 18-6-6 6-6"/></svg>`;
        break;
      case 'chevron-right':
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m9 18 6-6-6-6"/></svg>`;
        break;
      case 'alert-circle':
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><line x1="12" y1="8" x2="12" y2="12"/><line x1="12" y1="16" x2="12.01" y2="16"/></svg>`;
        break;
      case 'clock':
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><polyline points="12 6 12 12 16 14"/></svg>`;
        break;
      case 'mail':
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="16" x="2" y="4" rx="2"/><path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"/></svg>`;
        break;
      case 'phone':
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/></svg>`;
        break;
      case 'arrow-right':
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>`;
        break;
      case 'arrow-left':
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 12H5"/><path d="m12 19-7-7 7-7"/></svg>`;
        break;
      case 'twitter':
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"/></svg>`;
        break;
      case 'linkedin':
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"/><rect width="4" height="12" x="2" y="9"/><circle cx="4" cy="4" r="2"/></svg>`;
        break;
      case 'user':
        svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${this.size}" style="margin-top: -4px;" height="${this.size}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"/><circle cx="12" cy="7" r="4"/></svg>`;
        break;
      default:
        svg = '';
    }

    this.svgContent = this.sanitizer.bypassSecurityTrustHtml(svg);
  }
}
