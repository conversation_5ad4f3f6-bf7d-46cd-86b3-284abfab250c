<div
  class="ng-select ng-select-single ng-select-searchable ng-select-clearable ng-select-wrapper"
>
  <div class="ng-select-container" (click)="toggle($event)">
    <div class="ng-value-container">
      <div class="ng-placeholder" *ngIf="!hasValue()">
        <div class="flex items-center gap-2 w-full">
          <app-icon
            name="calendar"
            [size]="16"
            class="flex-shrink-0"
          ></app-icon>
          <span class="truncate">{{ placeholder }}</span>
        </div>
      </div>
      <div class="ng-value" *ngIf="hasValue()">
        <div class="flex items-center gap-2 w-full">
          <app-icon
            name="calendar"
            [size]="16"
            class="flex-shrink-0"
          ></app-icon>
          <span class="truncate">{{ getDisplayText() }}</span>
        </div>
      </div>
    </div>
    <span class="ng-arrow-wrapper">
      <app-icon name="chevron-down" [size]="16"></app-icon>
    </span>
  </div>

  <div
    class="date-picker-dropdown"
    *ngIf="isOpen"
    (click)="$event.stopPropagation()"
  >
    <div class="date-picker-tabs">
      <button
        type="button"
        class="tab-button"
        [class.active]="mode === 'single'"
        (click)="setMode('single', $event)"
      >
        Single Date
      </button>
      <button
        type="button"
        class="tab-button"
        [class.active]="mode === 'range'"
        (click)="setMode('range', $event)"
      >
        Date Range
      </button>
    </div>

    <div class="date-picker-body">
      <!-- Quick Selection Options -->
      <div class="quick-select-section">
        <div class="quick-select-grid">
          <button
            type="button"
            class="quick-select-button"
            (click)="selectPreset('today', $event)"
          >
            Today
          </button>
          <button
            type="button"
            class="quick-select-button"
            (click)="selectPreset('tomorrow', $event)"
          >
            Tomorrow
          </button>
          <button
            type="button"
            class="quick-select-button"
            (click)="selectPreset('thisWeek', $event)"
          >
            This Week
          </button>
          <button
            type="button"
            class="quick-select-button"
            (click)="selectPreset('nextWeek', $event)"
          >
            Next Week
          </button>
          <button
            type="button"
            class="quick-select-button"
            (click)="selectPreset('thisMonth', $event)"
          >
            This Month
          </button>
          <button
            type="button"
            class="quick-select-button"
            (click)="selectPreset('nextMonth', $event)"
          >
            Next Month
          </button>
        </div>
      </div>

      <!-- Single Date Picker -->
      <div *ngIf="mode === 'single'" class="single-date-container">
        <div class="shadcn-calendar">
          <div class="calendar-header">
            <button
              type="button"
              class="calendar-nav-button"
              (click)="prevMonth($event)"
              aria-label="Previous month"
            >
              <app-icon name="chevron-left" [size]="16"></app-icon>
            </button>
            <div class="calendar-title">{{ getMonthYearText() }}</div>
            <button
              type="button"
              class="calendar-nav-button"
              (click)="nextMonth($event)"
              aria-label="Next month"
            >
              <app-icon name="chevron-right" [size]="16"></app-icon>
            </button>
          </div>
          <div class="calendar-grid">
            <div class="calendar-weekdays">
              <div class="weekday">Su</div>
              <div class="weekday">Mo</div>
              <div class="weekday">Tu</div>
              <div class="weekday">We</div>
              <div class="weekday">Th</div>
              <div class="weekday">Fr</div>
              <div class="weekday">Sa</div>
            </div>
            <div class="calendar-days">
              <button
                *ngFor="let day of calendarDays"
                type="button"
                class="calendar-day"
                [class.outside-month]="!day.currentMonth"
                [class.today]="day.isToday"
                [class.selected]="day.selected"
                [disabled]="day.disabled"
                (click)="selectDate(day, $event)"
              >
                {{ day.day }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Date Range Picker -->
      <div *ngIf="mode === 'range'" class="range-date-container">
        <div class="shadcn-calendar">
          <div class="calendar-header">
            <button
              type="button"
              class="calendar-nav-button"
              (click)="prevMonth($event)"
              aria-label="Previous month"
            >
              <app-icon name="chevron-left" [size]="16"></app-icon>
            </button>
            <div class="calendar-title">{{ getMonthYearText() }}</div>
            <button
              type="button"
              class="calendar-nav-button"
              (click)="nextMonth($event)"
              aria-label="Next month"
            >
              <app-icon name="chevron-right" [size]="16"></app-icon>
            </button>
          </div>
          <div class="calendar-grid">
            <div class="calendar-weekdays">
              <div class="weekday">Su</div>
              <div class="weekday">Mo</div>
              <div class="weekday">Tu</div>
              <div class="weekday">We</div>
              <div class="weekday">Th</div>
              <div class="weekday">Fr</div>
              <div class="weekday">Sa</div>
            </div>
            <div class="calendar-days">
              <button
                *ngFor="let day of calendarDays"
                type="button"
                class="calendar-day"
                [class.outside-month]="!day.currentMonth"
                [class.today]="day.isToday"
                [class.selected]="day.selected"
                [class.range-start]="day.rangeStart"
                [class.range-end]="day.rangeEnd"
                [class.in-range]="day.inRange"
                [disabled]="day.disabled"
                (click)="selectDateRange(day, $event)"
              >
                {{ day.day }}
              </button>
            </div>
          </div>
        </div>
        <div class="range-inputs">
          <div class="range-input-group">
            <label class="date-label">From</label>
            <div class="date-display">
              {{ formatDateDisplay(rangeStartDate) || "Select start date" }}
            </div>
          </div>
          <div class="range-input-group">
            <label class="date-label">To</label>
            <div class="date-display">
              {{ formatDateDisplay(rangeEndDate) || "Select end date" }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="date-picker-footer">
      <button type="button" class="cancel-button" (click)="cancel($event)">
        Cancel
      </button>
      <button type="button" class="apply-button" (click)="apply($event)">
        Apply
      </button>
    </div>
  </div>
</div>
