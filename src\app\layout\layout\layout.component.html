<div class="d-flex flex-column min-vh-100">
  <header class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
      <div class="navbar-brand">App Logo</div>
      <nav class="navbar-nav ms-auto">
        <ul class="navbar-nav">
          <li class="nav-item">
            <a class="nav-link" routerLink="/">Dashboard</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" routerLink="/profile">Profile</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" routerLink="/auth/login">Login</a>
          </li>
        </ul>
      </nav>
    </div>
  </header>

  <main class="flex-grow-1 p-4">
    <router-outlet></router-outlet>
  </main>

  <footer class="bg-light text-center py-3">
    <p class="mb-0">&copy; Skill App</p>
  </footer>
</div>
