import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private isLoggedIn = false;

  constructor() {}

  isAuthenticated(): boolean {
    return this.isLoggedIn;
  }

  login(username: string, password: string): boolean {
    // This is a simple mock implementation
    // In a real app, you would validate credentials against a backend
    if (username && password) {
      this.isLoggedIn = true;
      // Store authentication token or user info in localStorage/sessionStorage
      localStorage.setItem('isLoggedIn', 'true');
      return true;
    }
    return false;
  }

  logout(): void {
    this.isLoggedIn = false;
    // Clear any stored authentication data
    localStorage.removeItem('isLoggedIn');
  }
}
