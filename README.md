# Skills Management Application

A modern Angular-based web application for managing and searching professional profiles and skills. This application is designed for law firms and professional services organizations to efficiently manage their talent pool and find the right professionals for specific projects.

## 🚀 Features

### Core Functionality

- **Profile Management**: Comprehensive professional profiles with detailed information
- **Advanced Search**: Multi-criteria search with filters for experience, location, availability, and more
- **Authentication System**: Secure login/logout functionality with route guards
- **Responsive Design**: Modern UI built with Angular Material and Bootstrap
- **Dashboard**: Overview of key metrics and quick access to main features

### Profile Features

- **Professional Information**: Name, title, contact details, experience years
- **Practice Areas**: Specialized legal practice areas with expertise levels
- **Skills**: Comprehensive skill sets and competencies
- **Industry Experience**: Sector-specific experience
- **Education & Bar Admissions**: Professional qualifications and certifications
- **Notable Matters**: Key projects and achievements
- **Availability Status**: Real-time availability tracking with match percentages

### Search & Filtering

- **Text Search**: Search across all profile fields
- **Experience Level Filtering**: Junior, Mid-level, Senior, Partner
- **Location-based Search**: Filter by geographic location
- **Language Filtering**: Multi-language support
- **Availability Filtering**: Current, next week, next month availability
- **Date-based Filtering**: Advanced date picker for availability

## 🛠️ Technology Stack

### Frontend Framework

- **Angular 16.2.0**: Modern TypeScript-based framework
- **Angular Material 16.2.14**: Material Design components
- **Bootstrap 5.3.2**: Responsive CSS framework
- **NgBootstrap 15.1.2**: Bootstrap components for Angular

### UI/UX Libraries

- **Chart.js 4.4.0**: Data visualization
- **Ng2-charts 5.0.3**: Angular wrapper for Chart.js
- **Lucide Angular 0.507.0**: Modern icon library
- **Tabler Icons 3.31.0**: Additional icon set
- **Bootstrap Icons 1.11.2**: Bootstrap icon library

### Form & Input Components

- **NgSelect 11**: Advanced select dropdowns
- **Ngx-slider-v2 17.0.0**: Range slider component
- **Reactive Forms**: Angular's reactive form system

### Utilities

- **Moment.js 2.29.4**: Date manipulation
- **XLSX 0.18.5**: Excel file handling
- **html2canvas 1.4.1**: HTML to canvas conversion
- **jsPDF 3.0.1**: PDF generation
- **UUID4 2.0.3**: Unique identifier generation

### Development Tools

- **TypeScript 5.1.3**: Type-safe JavaScript
- **ESLint 8.51.0**: Code linting
- **Angular CLI 16.2.12**: Development and build tools

## 📁 Project Structure

```
src/
├── app/
│   ├── core/                    # Core application logic
│   │   ├── auth/               # Authentication services
│   │   ├── models/             # Data models and interfaces
│   │   ├── services/           # Core services
│   │   └── theme/              # Theme configuration
│   ├── features/               # Feature modules
│   ├── layout/                 # Layout components
│   ├── screens/                # Main application screens
│   │   ├── auth/              # Authentication screens
│   │   ├── dashboard/         # Dashboard screen
│   │   ├── profile/           # Profile management screens
│   │   └── not-found/         # 404 error page
│   └── shared/                # Shared components and modules
│       └── components/        # Reusable UI components
├── assets/                    # Static assets
└── styles.css                # Global styles
```

## 🚀 Getting Started

### Prerequisites

- **Node.js 20.x**: Required for Angular development
- **Yarn**: Package manager (recommended) or npm
- **Angular CLI**: Global installation recommended

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd skills
   ```

2. **Install dependencies**

   ```bash
   yarn install
   # or
   npm install
   ```

3. **Start the development server**

   ```bash
   yarn start
   # or
   npm start
   ```

4. **Open your browser**
   Navigate to `http://localhost:4200`

### Build Commands

```bash
# Development build
yarn build

# Production build
yarn build --configuration production

# Watch mode for development
yarn watch

# Run tests
yarn test
```

## 🔧 Configuration

### Environment Setup

The application uses Angular's environment system. Create environment files in `src/environments/`:

- `environment.ts` - Development environment
- `environment.prod.ts` - Production environment

### Authentication

Currently uses a mock authentication service. In production, integrate with:

- Azure AD (MSAL is already included)
- Custom authentication backend
- OAuth providers

## 📊 Data Models

### Profile Data Structure

```typescript
interface ProfileData {
  id: number;
  name: string;
  title: string;
  email: string;
  phone: string;
  location: string;
  yearsExperience: number;
  practiceAreas: PracticeArea[];
  skills: Skill[];
  industryExperience: IndustryExperience[];
  education: Education[];
  barAdmissions: BarAdmission[];
  notableMatters: NotableMatter[];
  availability: Availability;
}
```

## 🎨 UI Components

### Shared Components

- **Icon Component**: Unified icon system
- **Date Picker**: Advanced date selection
- **Lucide Icon**: Modern icon integration

### Layout Components

- **Layout Component**: Main application layout
- **Navigation**: Responsive navigation system

## 🔒 Security

### Authentication

- Route guards for protected routes
- Mock authentication service (replace with real implementation)
- Local storage for session management

### Data Protection

- Input validation and sanitization
- XSS protection through Angular's built-in security features

## 🧪 Testing

The application includes:

- **Unit Tests**: Component and service testing
- **E2E Tests**: End-to-end testing setup
- **Test Coverage**: Comprehensive test coverage

Run tests with:

```bash
yarn test
```

## 📦 Deployment

### Production Build

```bash
yarn build --configuration production
```

### Deployment Options

- **Static Hosting**: Deploy to Netlify, Vercel, or similar
- **Docker**: Containerized deployment
- **Cloud Platforms**: Azure, AWS, or Google Cloud

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:

- Create an issue in the repository
- Contact the development team
- Check the documentation

## 🔄 Version History

- **v0.0.0**: Initial release with core functionality
  - Profile management system
  - Advanced search and filtering
  - Authentication system
  - Responsive UI design

## 🚧 Roadmap

### Planned Features

- [ ] Real-time notifications
- [ ] Advanced analytics dashboard
- [ ] Export functionality (PDF, Excel)
- [ ] Mobile application
- [ ] API integration
- [ ] Multi-language support
- [ ] Advanced reporting
- [ ] Integration with HR systems

### Technical Improvements

- [ ] Performance optimization
- [ ] Accessibility improvements
- [ ] Enhanced security features
- [ ] Automated testing
- [ ] CI/CD pipeline
- [ ] Monitoring and logging
