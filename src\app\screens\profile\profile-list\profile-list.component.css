/* Common styles for all ng-select placeholders */
.icon-info-circle ::ng-deep .ng-placeholder,
.icon-map-pin ::ng-deep .ng-placeholder,
.icon-book ::ng-deep .ng-placeholder {
  display: flex !important;
  align-items: center !important;
  position: relative !important;
  padding-left: 24px !important; /* Make space for the icon */
}

/* Hide placeholder when an option is selected */
.icon-info-circle ::ng-deep .ng-has-value .ng-placeholder,
.icon-map-pin ::ng-deep .ng-has-value .ng-placeholder,
.icon-book ::ng-deep .ng-has-value .ng-placeholder {
  display: none !important;
}

/* Date picker container styles */
.date-picker-container {
  position: relative;
  width: 100%;
}

.date-picker-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
  color: var(--content3);
  pointer-events: none;
}

.date-picker {
  padding-left: 36px !important;
  color: var(--content1);
  background-color: var(--backgroundPrimary);
}

/* Style the date picker calendar icon */
.date-picker::-webkit-calendar-picker-indicator {
  opacity: 0.6;
  cursor: pointer;
}

/* Style the date picker placeholder */
.date-picker::placeholder {
  color: var(--content3);
}

/* Add icon using ::before pseudo-element with SVG background images */
.icon-info-circle ::ng-deep .ng-placeholder::before,
.icon-map-pin ::ng-deep .ng-placeholder::before,
.icon-book ::ng-deep .ng-placeholder::before,
.icon-calendar ::ng-deep .ng-placeholder::before {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  display: block;
}

/* Individual icon styles using SVG data URLs for Lucide icons */
.icon-info-circle ::ng-deep .ng-placeholder::before {
  content: "";
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'/%3E%3Cpath d='M12 16v-4'/%3E%3Cpath d='M12 8h.01'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
}

.icon-map-pin ::ng-deep .ng-placeholder::before {
  content: "";
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z'/%3E%3Ccircle cx='12' cy='10' r='3'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
}

.icon-book ::ng-deep .ng-placeholder::before {
  content: "";
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
}

.icon-calendar ::ng-deep .ng-placeholder::before {
  content: "";
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M8 2v3'/%3E%3Cpath d='M16 2v3'/%3E%3Cpath d='M3 10h18'/%3E%3Cpath d='M3 5h18v16H3z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
}
