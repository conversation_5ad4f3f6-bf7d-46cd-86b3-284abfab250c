<div>
  <!-- Loading State -->
  <div
    *ngIf="loading"
    class="d-flex flex-column justify-content-center align-items-center py-20"
  >
    <div class="loading loading-spinner loading-lg text-primary mb-4"></div>
    <div class="text-content2 animate-pulse">
      Loading profile information...
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="error" class="alert alert-error shadow-lg mb-8">
    <div>
      <app-icon name="alert-circle" [size]="24"></app-icon>
      <span
        >Profile not found or error loading profile. Please try again
        later.</span
      >
    </div>
    <div class="flex-none">
      <a routerLink="/profile" class="btn btn-sm">Back to Profiles</a>
    </div>
  </div>

  <div *ngIf="!loading && !error && profileData">
    <!-- Back Button -->
    <div class="mb-6">
      <a
        routerLink="/profile"
        class="inline-flex align-items-center text-content2 hover-text-content1 transition-colors gap-2"
      >
        <app-icon name="arrow-left" [size]="20"></app-icon>
        <span class="font-medium">Back to Profiles</span>
      </a>
    </div>

    <!-- Profile Header Section (Hero Card, full width) -->
    <div class="container">
      <div
        class="card bg-backgroundPrimary shadow-sm mb-4 max-w-full rounded-lg border border-border hover-shadow"
      >
        <div class="card-body p-4">
          <div class="row align-items-center">
            <div class="d-flex align-items-center">
              <div class="me-4">
                <div
                  style="width: 90px; height: 90px"
                  class="rounded-circle bg-blue-3 text-blue-11 d-flex align-items-center justify-content-center fs-2 fw-medium"
                >
                  {{ profileData.initials }}
                </div>
              </div>
              <div class="col">
                <h1 class="h4 fw-semibold mb-1 text-content1">
                  {{ profileData.name }}
                </h1>
                <div class="mb-2 text-content1">{{ profileData.title }}</div>
                <div class="d-flex text-sm">
                  <div class="col-6 d-flex flex-column fw-normal text-sm">
                    <div class="mb-1 text-content2">
                      <app-icon
                        name="map-pin"
                        [size]="16"
                        class="text-content3"
                      ></app-icon>
                      {{ profileData.location }}
                    </div>
                    <div class="mb-1 text-content2 flex-nowrap text-nowrap">
                      <app-icon
                        name="mail"
                        [size]="16"
                        class="text-content3"
                      ></app-icon>
                      {{ profileData.email }}
                    </div>
                    <div>
                      <span
                        class="badge-flat badge-flat-success border border-success align-middle"
                        style="font-size: 0.85em; font-weight: 400"
                      >
                        <span
                          class="me-1"
                          style="
                            display: inline-block;
                            width: 8px;
                            height: 8px;
                            background: #22c55e;
                            border-radius: 50%;
                          "
                        ></span
                        >{{ profileData.availability.status }}</span
                      >
                    </div>
                  </div>
                  <div
                    class="col-6 d-flex flex-column justify-content-end align-items-end"
                  >
                    <div class="mb-1 text-content2">
                      <app-icon
                        name="clock"
                        [size]="16"
                        class="text-content3"
                      ></app-icon>
                      {{ profileData.yearsWithFirm }} years with firm
                    </div>
                    <div class="mb-2 text-content2">
                      <app-icon
                        name="phone"
                        [size]="16"
                        class="text-content3"
                      ></app-icon>
                      {{ profileData.phone }}
                    </div>
                    <span
                      class="badge-flat badge-flat-primary border align-middle"
                      style="font-size: 0.85em"
                    >
                      <app-icon
                        name="info-circle"
                        [size]="12"
                        class="me-1"
                      ></app-icon
                      >{{ profileData.availability.matchPercentage }}%
                      match</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Main Content and Contact Card in Bootstrap Container -->
    <div class="container">
      <div class="row">
        <!-- Main Content Left -->
        <div class="col-lg-9 col-md-8">
          <!-- Expertise Section -->
          <div
            class="card bg-backgroundPrimary shadow-md mb-6 rounded-lg border border-border max-w-full hover-shadow"
          >
            <div class="card-body p-6">
              <div class="d-flex align-items-center gap-2 mb-4">
                <h2 class="text-xl font-semibold text-content1">Expertise</h2>
              </div>
              <!-- Practice Areas -->
              <div class="mb-6">
                <div class="d-flex align-items-center gap-2 mb-3">
                  <app-icon
                    name="briefcase"
                    [size]="16"
                    class="text-content3"
                  ></app-icon>
                  <h3 class="text-base font-medium text-content1">
                    Practice Areas
                  </h3>
                </div>
                <div>
                  <div
                    *ngFor="let area of profileData.practiceAreas"
                    class="d-flex justify-content-between align-items-center px-2 pl-6 py-2 hover-bg-backgroundSecondary rounded-md transition-colors group"
                  >
                    <span
                      class="font-medium text-sm text-content2 group-hover-text-content1 transition-colors"
                      >{{ area.name }}</span
                    >
                    <span
                      class="badge-flat border align-middle"
                      [ngClass]="{
                        'badge-flat-success': area.level === 'Expert',
                        'badge-flat-secondary': area.level === 'Advanced',
                        'badge-flat-warning': area.level === 'Intermediate',
                        'badge-flat-danger': area.level === 'Beginner'
                      }"
                      [style.background-color]="
                        area.level === 'Advanced' ? '#f9f1fe' : ''
                      "
                      [style.color]="area.level === 'Advanced' ? '#793ab5' : ''"
                      style="font-size: 0.85em; font-weight: 400"
                      >{{ area.level }}</span
                    >
                  </div>
                </div>
              </div>
              <!-- Skills Section Title -->
              <div class="d-flex align-items-center gap-2 mb-3 mt-8">
                <h3 class="text-lg text-content1">Skills</h3>
              </div>
              <!-- Skills -->
              <div class="mb-6">
                <div class="d-flex align-items-center gap-2 mb-3">
                  <app-icon
                    name="info-circle"
                    [size]="16"
                    class="text-content3"
                  ></app-icon>
                  <h3 class="text-base font-medium text-content1">
                    Transaction Types
                  </h3>
                </div>
                <div class="border border-border rounded-lg p-4">
                  <div class="badge-group">
                    <span
                      *ngFor="let skill of profileData.skills"
                      class="inline-flex align-items-center px-2 py-0.5 rounded-full text-xs bg-backgroundSecondary text-content2 border border-border cursor-default mr-2 mb-2 hover-bg-blue-3 hover-text-blue-11 hover-border-blue-5 transition-colors"
                      >{{ skill.name }}</span
                    >
                  </div>
                </div>
              </div>
              <!-- Industry Experience -->
              <div>
                <div class="d-flex align-items-center gap-2 mb-3">
                  <app-icon
                    name="briefcase"
                    [size]="16"
                    class="text-content3"
                  ></app-icon>
                  <h3 class="text-base font-medium text-content1">
                    Industry Experience
                  </h3>
                </div>
                <div class="border border-border rounded-lg p-4">
                  <div class="badge-group">
                    <span
                      *ngFor="let industry of profileData.industryExperience"
                      class="inline-flex align-items-center px-2 py-0.5 rounded-full text-xs bg-backgroundSecondary text-content2 border border-border cursor-default mr-2 mb-2 hover-bg-blue-3 hover-text-blue-11 hover-border-blue-5 transition-colors"
                      >{{ industry.name }}</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- Background Section -->
          <div
            class="card bg-backgroundPrimary shadow-md mb-6 rounded-lg border border-border max-w-full hover-shadow"
          >
            <div class="card-body p-6">
              <div class="d-flex align-items-center gap-2">
                <h2 class="text-xl font-semibold text-content1">Background</h2>
              </div>

              <!-- Professional Summary -->
              <div class="mb-3">
                <div class="d-flex align-items-center gap-2">
                  <app-icon
                    name="info-circle"
                    [size]="16"
                    class="text-content3"
                  ></app-icon>
                  <h3 class="text-base font-medium text-content1 mt-2">
                    Professional Summary
                  </h3>
                </div>
                <p class="text-content2 ml-7">
                  {{ profileData.professionalSummary }}
                </p>
              </div>

              <!-- Education -->
              <div class="mb-3">
                <div class="d-flex align-items-center gap-2">
                  <app-icon
                    name="book"
                    [size]="16"
                    class="text-content3"
                  ></app-icon>
                  <h3 class="text-base font-medium text-content1 mt-2">
                    Education
                  </h3>
                </div>
                <ul class="space-y-2 ml-7">
                  <li
                    *ngFor="let edu of profileData.education"
                    class="text-content2"
                  >
                    <span class="font-medium">{{ edu.degree }}</span
                    >, {{ edu.institution }}
                  </li>
                </ul>
              </div>

              <!-- Bar Admissions -->
              <div>
                <div class="d-flex align-items-center gap-2 mb-1">
                  <app-icon
                    name="briefcase"
                    [size]="16"
                    class="text-content3"
                  ></app-icon>
                  <h3 class="text-base font-medium text-content1 mt-2">
                    Bar Admissions
                  </h3>
                </div>
                <div class="badge-group ml-7">
                  <span
                    *ngFor="let admission of profileData.barAdmissions"
                    class="inline-flex align-items-center px-2 py-0.5 rounded-full text-xs bg-backgroundSecondary text-content2 border border-border cursor-default mr-2 mb-2 hover-bg-blue-3 hover-text-blue-11 hover-border-blue-5 transition-colors"
                    >{{ admission.state }}</span
                  >
                </div>
              </div>
            </div>
          </div>

          <!-- Notable Matters Section -->
          <div
            class="card bg-backgroundPrimary shadow-md rounded-lg border border-border max-w-full hover-shadow"
          >
            <div class="card-body p-6">
              <div class="d-flex align-items-center gap-2 mb-3">
                <h2 class="text-xl font-semibold text-content1">
                  Notable Matters
                </h2>
              </div>

              <!-- Notable Matters -->
              <div class="space-y-4">
                <div
                  *ngFor="let matter of profileData.notableMatters"
                  class="border-bottom border-border pb-1 last-border-0 last-pb-0 hover-bg-backgroundSecondary px-2 py-1 rounded-md -mx-2 mb-2 transition-colors group"
                >
                  <div
                    class="d-flex justify-content-between align-items-start mb-0"
                  >
                    <h3
                      class="text-sm font-semibold text-content1 group-hover-text-blue-11 transition-colors"
                    >
                      {{ matter.title }}
                    </h3>
                    <span class="badge-flat badge-flat-primary">
                      {{ matter.year }}
                    </span>
                  </div>
                  <p
                    class="text-content2 text-sm mb-0 group-hover-text-content1 transition-colors"
                  >
                    {{ matter.description }}
                  </p>
                  <span
                    class="inline-flex align-items-center px-2 py-0.5 rounded-full text-xs bg-backgroundSecondary text-content2 border border-border cursor-default mr-2 mb-2 hover-bg-blue-3 hover-text-blue-11 hover-border-blue-5 transition-colors"
                  >
                    <app-icon
                      name="user"
                      [size]="12"
                      class="mr-1.5 text-content3"
                    ></app-icon>
                    {{ matter.role }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- Contact Card Right -->
        <div class="col-lg-3 col-md-4">
          <div class="position-sticky" style="top: 1rem">
            <div
              class="card bg-backgroundPrimary shadow-md rounded-lg border border-border hover-shadow"
            >
              <div class="card-body p-6">
                <div class="d-flex align-items-center gap-2 mb-4">
                  <h2 class="text-xl font-semibold text-content1">
                    Contact Information
                  </h2>
                </div>
                <div class="d-flex flex-column">
                  <a
                    href="mailto:{{ profileData.email }}"
                    class="d-flex align-items-center gap-2 px-3 py-1 hover-bg-backgroundSecondary rounded-md transition-all group hover-shadow-sm"
                  >
                    <app-icon
                      name="mail"
                      [size]="16"
                      class="text-content3 group-hover-text-blue-9 transition-colors"
                    ></app-icon>
                    <span
                      class="text-sm text-content2 group-hover-text-content1 transition-colors"
                      >{{ profileData.email }}</span
                    >
                  </a>
                  <a
                    href="tel:{{ profileData.phone }}"
                    class="d-flex align-items-center gap-2 px-3 py-1 hover-bg-backgroundSecondary rounded-md transition-all group hover-shadow-sm"
                  >
                    <app-icon
                      name="phone"
                      [size]="16"
                      class="text-content3 group-hover-text-blue-9 transition-colors"
                    ></app-icon>
                    <span
                      class="text-sm text-content2 group-hover-text-content1 transition-colors"
                      >{{ profileData.phone }}</span
                    >
                  </a>
                  <div
                    class="d-flex align-items-center gap-2 px-3 py-1 hover-bg-backgroundSecondary rounded-md transition-all group cursor-default hover-shadow-sm"
                  >
                    <app-icon
                      name="map-pin"
                      [size]="16"
                      class="text-content3 group-hover-text-blue-9 transition-colors"
                    ></app-icon>
                    <span
                      class="text-sm text-content2 group-hover-text-content1 transition-colors"
                      >{{ profileData.location }}</span
                    >
                  </div>
                  <div class="mt-4 pt-4 border-t border-border">
                    <div class="d-flex justify-content-center space-x-5">
                      <a
                        href="#"
                        class="text-content3 hover-text-blue-9 transition-all transform hover-scale-110 p-2.5 rounded-full hover-bg-blue-2 hover-shadow-sm"
                        title="Twitter Profile"
                      >
                        <app-icon name="twitter" [size]="18"></app-icon>
                      </a>
                      <a
                        href="#"
                        class="text-content3 hover-text-blue-11 transition-all transform hover-scale-110 p-2.5 rounded-full hover-bg-blue-2 hover-shadow-sm"
                        title="LinkedIn Profile"
                      >
                        <app-icon name="linkedin" [size]="18"></app-icon>
                      </a>
                      <a
                        href="mailto:{{ profileData.email }}"
                        class="text-content3 hover-text-error transition-all transform hover-scale-110 p-2.5 rounded-full hover-bg-red-2 hover-shadow-sm"
                        title="Send Email"
                      >
                        <app-icon name="mail" [size]="18"></app-icon>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
