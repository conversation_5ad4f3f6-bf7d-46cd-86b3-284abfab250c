import {
  Component,
  ElementRef,
  EventEmitter,
  HostBinding,
  HostListener,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IconComponent } from '../icon/icon.component';

export interface DateSelection {
  type: 'single' | 'range';
  singleDate?: string | null;
  startDate?: string | null;
  endDate?: string | null;
}

export interface CalendarDay {
  date: Date;
  day: number;
  month: number;
  year: number;
  currentMonth: boolean;
  isToday: boolean;
  selected: boolean;
  disabled: boolean;
  rangeStart?: boolean;
  rangeEnd?: boolean;
  inRange?: boolean;
}

@Component({
  selector: 'app-date-picker',
  templateUrl: './date-picker.component.html',
  styleUrls: ['./date-picker.component.css'],
  standalone: true,
  imports: [CommonModule, FormsModule, IconComponent],
})
export class DatePickerComponent implements OnInit {
  @HostBinding('style.display') display = 'block';
  @HostBinding('style.width') width = '100%';
  @HostBinding('style.max-width') maxWidth = '100%';

  @Input() placeholder = 'Pick a date';
  @Input() initialSelection: DateSelection | null = null;
  @Output() dateSelected = new EventEmitter<DateSelection>();

  isOpen = false;
  mode: 'single' | 'range' = 'single';
  singleDate: string | null = null;
  rangeStartDate: string | null = null;
  rangeEndDate: string | null = null;

  // Calendar properties
  currentMonth: Date = new Date();
  calendarDays: CalendarDay[] = [];

  constructor(private elementRef: ElementRef) {}

  @HostListener('document:click', ['$event'])
  onClickOutside(event: MouseEvent) {
    // Close dropdown when clicking outside
    if (this.isOpen && !this.elementRef.nativeElement.contains(event.target)) {
      this.isOpen = false;
    }
  }

  ngOnInit(): void {
    if (this.initialSelection) {
      this.mode = this.initialSelection.type;
      if (this.initialSelection.type === 'single') {
        this.singleDate = this.initialSelection.singleDate || null;
        if (this.singleDate) {
          this.currentMonth = new Date(this.singleDate);
        }
      } else {
        this.rangeStartDate = this.initialSelection.startDate || null;
        this.rangeEndDate = this.initialSelection.endDate || null;
        if (this.rangeStartDate) {
          this.currentMonth = new Date(this.rangeStartDate);
        }
      }
    }

    this.generateCalendarDays();
  }

  generateCalendarDays(): void {
    const firstDayOfMonth = new Date(
      this.currentMonth.getFullYear(),
      this.currentMonth.getMonth(),
      1
    );
    const lastDayOfMonth = new Date(
      this.currentMonth.getFullYear(),
      this.currentMonth.getMonth() + 1,
      0
    );

    const daysInMonth = lastDayOfMonth.getDate();
    const firstDayOfWeek = firstDayOfMonth.getDay(); // 0 = Sunday, 1 = Monday, etc.

    // Get days from previous month to fill the first week
    const daysFromPrevMonth = firstDayOfWeek;
    const prevMonth = new Date(
      this.currentMonth.getFullYear(),
      this.currentMonth.getMonth() - 1,
      1
    );
    const daysInPrevMonth = new Date(
      this.currentMonth.getFullYear(),
      this.currentMonth.getMonth(),
      0
    ).getDate();

    // Get days from next month to fill the last week
    const daysFromNextMonth = 6 - lastDayOfMonth.getDay();

    // Generate calendar days
    this.calendarDays = [];

    // Add days from previous month
    for (
      let i = daysInPrevMonth - daysFromPrevMonth + 1;
      i <= daysInPrevMonth;
      i++
    ) {
      const date = new Date(prevMonth.getFullYear(), prevMonth.getMonth(), i);

      this.calendarDays.push(this.createCalendarDay(date, false));
    }

    // Add days from current month
    for (let i = 1; i <= daysInMonth; i++) {
      const date = new Date(
        this.currentMonth.getFullYear(),
        this.currentMonth.getMonth(),
        i
      );

      this.calendarDays.push(this.createCalendarDay(date, true));
    }

    // Add days from next month
    const nextMonth = new Date(
      this.currentMonth.getFullYear(),
      this.currentMonth.getMonth() + 1,
      1
    );

    for (let i = 1; i <= daysFromNextMonth; i++) {
      const date = new Date(nextMonth.getFullYear(), nextMonth.getMonth(), i);

      this.calendarDays.push(this.createCalendarDay(date, false));
    }
  }

  toggle(event?: MouseEvent): void {
    // Stop event propagation to prevent document click handler from closing the dropdown
    if (event) {
      event.stopPropagation();
    }

    this.isOpen = !this.isOpen;

    // Ensure dropdown is visible within viewport after opening
    if (this.isOpen) {
      // Generate calendar days when opening
      this.generateCalendarDays();

      setTimeout(() => {
        const dropdown = document.querySelector(
          '.date-picker-dropdown'
        ) as HTMLElement;
        if (dropdown) {
          const rect = dropdown.getBoundingClientRect();
          const viewportHeight = window.innerHeight;

          // If dropdown extends beyond viewport bottom
          if (rect.bottom > viewportHeight) {
            // Position above if there's more space, otherwise limit height
            const spaceAbove = rect.top;
            const spaceBelow = viewportHeight - rect.top;

            if (spaceAbove > spaceBelow && spaceAbove > 300) {
              dropdown.style.top = 'auto';
              dropdown.style.bottom = 'calc(100% + 4px)';
            } else {
              dropdown.style.maxHeight = `${viewportHeight - rect.top - 20}px`;
            }
          }
        }
      }, 0);
    }
  }

  createCalendarDay(date: Date, currentMonth: boolean): CalendarDay {
    // Create a date at midnight in local timezone to avoid timezone issues
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Create a copy of the date with time set to midnight in local timezone
    const localDate = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
      0,
      0,
      0,
      0
    );

    const day: CalendarDay = {
      date: localDate,
      day: date.getDate(),
      month: date.getMonth(),
      year: date.getFullYear(),
      currentMonth: currentMonth,
      isToday: this.isSameDay(localDate, today),
      selected: false,
      disabled: false,
    };

    // Check if day is selected (single date mode)
    if (this.mode === 'single' && this.singleDate) {
      const selectedDate = this.parseDate(this.singleDate);
      day.selected = this.isSameDay(localDate, selectedDate);
    }

    // Check if day is in range (range mode)
    if (this.mode === 'range') {
      if (this.rangeStartDate) {
        const startDate = this.parseDate(this.rangeStartDate);
        day.rangeStart = this.isSameDay(localDate, startDate);

        if (this.rangeEndDate) {
          const endDate = this.parseDate(this.rangeEndDate);
          day.rangeEnd = this.isSameDay(localDate, endDate);

          // Check if day is in range
          day.inRange =
            localDate.getTime() > startDate.getTime() &&
            localDate.getTime() < endDate.getTime();
        }
      }
    }

    return day;
  }

  // Helper method to parse ISO date string to local date at midnight
  parseDate(dateString: string): Date {
    const parts = dateString.split('-');
    return new Date(
      parseInt(parts[0], 10),
      parseInt(parts[1], 10) - 1, // Month is 0-based
      parseInt(parts[2], 10),
      0,
      0,
      0,
      0
    );
  }

  // Helper method to check if two dates are the same day
  isSameDay(date1: Date, date2: Date): boolean {
    return (
      date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate()
    );
  }

  prevMonth(event?: MouseEvent): void {
    if (event) {
      event.stopPropagation();
    }
    this.currentMonth = new Date(
      this.currentMonth.getFullYear(),
      this.currentMonth.getMonth() - 1,
      1
    );
    this.generateCalendarDays();
  }

  nextMonth(event?: MouseEvent): void {
    if (event) {
      event.stopPropagation();
    }
    this.currentMonth = new Date(
      this.currentMonth.getFullYear(),
      this.currentMonth.getMonth() + 1,
      1
    );
    this.generateCalendarDays();
  }

  getMonthYearText(): string {
    return this.currentMonth.toLocaleDateString('en-US', {
      month: 'long',
      year: 'numeric',
    });
  }

  selectDate(day: CalendarDay, event?: MouseEvent): void {
    if (event) {
      event.stopPropagation();
    }

    if (day.disabled) return;

    // Format the date as YYYY-MM-DD ensuring local timezone
    const year = day.date.getFullYear();
    const month = String(day.date.getMonth() + 1).padStart(2, '0'); // Month is 0-based
    const date = String(day.date.getDate()).padStart(2, '0');
    this.singleDate = `${year}-${month}-${date}`;

    this.generateCalendarDays();

    // Don't auto-apply, let the user click Apply button
  }

  selectDateRange(day: CalendarDay, event?: MouseEvent): void {
    if (event) {
      event.stopPropagation();
    }

    if (day.disabled) return;

    // Format the date as YYYY-MM-DD ensuring local timezone
    const year = day.date.getFullYear();
    const month = String(day.date.getMonth() + 1).padStart(2, '0'); // Month is 0-based
    const date = String(day.date.getDate()).padStart(2, '0');
    const selectedDate = `${year}-${month}-${date}`;

    if (!this.rangeStartDate || (this.rangeStartDate && this.rangeEndDate)) {
      // Start new range
      this.rangeStartDate = selectedDate;
      this.rangeEndDate = null;

      // Update the current month to the selected date's month
      this.currentMonth = new Date(day.date);
    } else {
      // Complete range
      const startDate = this.parseDate(this.rangeStartDate);
      const clickedDate = day.date;

      if (clickedDate < startDate) {
        // If clicked date is before start date, swap them
        this.rangeEndDate = this.rangeStartDate;
        this.rangeStartDate = selectedDate;
      } else {
        this.rangeEndDate = selectedDate;
      }

      // Don't auto-apply, let the user click Apply button
    }

    this.generateCalendarDays();
  }

  formatDateDisplay(dateString: string | null): string {
    if (!dateString) return '';

    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  }

  setMode(mode: 'single' | 'range', event?: MouseEvent): void {
    if (event) {
      event.stopPropagation();
    }
    this.mode = mode;
  }

  hasValue(): boolean {
    return (
      (this.mode === 'single' && !!this.singleDate) ||
      (this.mode === 'range' && (!!this.rangeStartDate || !!this.rangeEndDate))
    );
  }

  getDisplayText(): string {
    if (this.mode === 'single' && this.singleDate) {
      return this.formatDate(this.singleDate);
    } else if (
      this.mode === 'range' &&
      this.rangeStartDate &&
      this.rangeEndDate
    ) {
      return `${this.formatDate(this.rangeStartDate)} - ${this.formatDate(
        this.rangeEndDate
      )}`;
    } else if (this.mode === 'range' && this.rangeStartDate) {
      return `${this.formatDate(this.rangeStartDate)} - Select end date`;
    }
    return this.placeholder;
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  }

  // Helper method to format date as YYYY-MM-DD
  formatDateString(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Month is 0-based
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  selectPreset(preset: string, event?: MouseEvent): void {
    if (event) {
      event.stopPropagation();
    }
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayStr = this.formatDateString(today);

    // Handle both single date and range modes
    switch (preset) {
      case 'today':
        if (this.mode === 'single') {
          this.singleDate = todayStr;
        } else {
          this.rangeStartDate = todayStr;
          this.rangeEndDate = todayStr;
        }
        break;

      case 'tomorrow':
        const tomorrow = new Date(today);
        tomorrow.setDate(today.getDate() + 1);
        const tomorrowStr = this.formatDateString(tomorrow);

        if (this.mode === 'single') {
          this.singleDate = tomorrowStr;
        } else {
          this.rangeStartDate = tomorrowStr;
          this.rangeEndDate = tomorrowStr;
        }
        break;

      case 'thisWeek':
        // Get the current week (Sunday to Saturday)
        const startOfThisWeek = new Date(today);
        startOfThisWeek.setDate(today.getDate() - today.getDay()); // Sunday
        const endOfThisWeek = new Date(startOfThisWeek);
        endOfThisWeek.setDate(startOfThisWeek.getDate() + 6); // Saturday

        if (this.mode === 'single') {
          this.singleDate = todayStr; // Just use today for single mode
        } else {
          this.rangeStartDate = this.formatDateString(startOfThisWeek);
          this.rangeEndDate = this.formatDateString(endOfThisWeek);
        }
        break;

      case 'nextWeek':
        // Get next week (Sunday to Saturday)
        const startOfNextWeek = new Date(today);
        startOfNextWeek.setDate(today.getDate() + (7 - today.getDay())); // Next Sunday
        const endOfNextWeek = new Date(startOfNextWeek);
        endOfNextWeek.setDate(startOfNextWeek.getDate() + 6); // Next Saturday

        if (this.mode === 'single') {
          // Use next Monday for single mode
          const nextMonday = new Date(today);
          nextMonday.setDate(today.getDate() + ((8 - today.getDay()) % 7));
          this.singleDate = this.formatDateString(nextMonday);
        } else {
          this.rangeStartDate = this.formatDateString(startOfNextWeek);
          this.rangeEndDate = this.formatDateString(endOfNextWeek);
        }
        break;

      case 'thisMonth':
        // Get the current month (1st to last day)
        const startOfThisMonth = new Date(
          today.getFullYear(),
          today.getMonth(),
          1
        );
        const endOfThisMonth = new Date(
          today.getFullYear(),
          today.getMonth() + 1,
          0
        );

        if (this.mode === 'single') {
          this.singleDate = todayStr; // Just use today for single mode
        } else {
          this.rangeStartDate = this.formatDateString(startOfThisMonth);
          this.rangeEndDate = this.formatDateString(endOfThisMonth);
        }
        break;

      case 'nextMonth':
        // Get next month (1st to last day)
        const startOfNextMonth = new Date(
          today.getFullYear(),
          today.getMonth() + 1,
          1
        );
        const endOfNextMonth = new Date(
          today.getFullYear(),
          today.getMonth() + 2,
          0
        );

        if (this.mode === 'single') {
          // Use 1st day of next month for single mode
          this.singleDate = this.formatDateString(startOfNextMonth);
        } else {
          this.rangeStartDate = this.formatDateString(startOfNextMonth);
          this.rangeEndDate = this.formatDateString(endOfNextMonth);
        }
        break;
    }

    // Update calendar days to reflect selection
    this.generateCalendarDays();

    // Don't auto-apply, let the user click Apply button
  }

  apply(event?: MouseEvent): void {
    if (event) {
      event.stopPropagation();
    }

    let selection: DateSelection;

    if (this.mode === 'single') {
      selection = {
        type: 'single',
        singleDate: this.singleDate,
      };
    } else {
      selection = {
        type: 'range',
        startDate: this.rangeStartDate,
        endDate: this.rangeEndDate,
      };
    }

    this.dateSelected.emit(selection);
    this.isOpen = false;
  }

  cancel(event?: MouseEvent): void {
    if (event) {
      event.stopPropagation();
    }

    // Reset to initial values if provided
    if (this.initialSelection) {
      this.mode = this.initialSelection.type;
      if (this.initialSelection.type === 'single') {
        this.singleDate = this.initialSelection.singleDate || null;
      } else {
        this.rangeStartDate = this.initialSelection.startDate || null;
        this.rangeEndDate = this.initialSelection.endDate || null;
      }
    } else {
      // Otherwise reset to defaults
      this.mode = 'single';
      this.singleDate = null;
      this.rangeStartDate = null;
      this.rangeEndDate = null;
    }

    this.isOpen = false;
  }
}
