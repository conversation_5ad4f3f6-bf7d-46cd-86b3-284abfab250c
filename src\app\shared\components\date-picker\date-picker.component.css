/* ng-select-like Date Picker Styles */
:host {
  display: block !important;
  width: 100% !important;
  max-width: 100% !important;
}

.ng-select-wrapper {
  position: relative;
  width: 100% !important;
  max-width: 100% !important;
  display: block !important;
}

/* Container styles to match ng-select */
.ng-select-container {
  display: flex !important;
  align-items: center !important;
  width: 100% !important;
  min-height: 2.5rem !important;
  height: 2.5rem !important;
  padding-left: 0.5rem !important;
  padding-right: 0.5rem !important;
  background-color: #ffffff;
  border: 1px solid #e5e7eb !important;
  border-radius: calc(var(--radius) - 2px) !important;
  color: #000000;
  font-size: 0.875rem !important;
  cursor: pointer;
  transition: border-color 150ms ease-in-out, box-shadow 150ms ease-in-out !important;
  box-sizing: border-box !important;
}

.ng-select-container:hover {
  border-color: #000000 !important;
}

.ng-select-container:focus {
  outline: none !important;
  border-color: #000000 !important;
  box-shadow: 0 0 0 2px #ffffff, 0 0 0 4px #000000 !important;
}

/* Value container */
.ng-value-container {
  display: flex !important;
  align-items: center !important;
  flex: 1 !important;
  width: 100% !important;
  overflow: visible !important;
  padding-left: 0 !important;
}

/* Placeholder styles */
.ng-placeholder {
  color: #000000 !important;
  opacity: 1 !important;
  visibility: visible !important;
  align-items: center !important;
  width: 100% !important;
}

.ng-placeholder > div {
  width: 100% !important;
  display: flex !important;
  align-items: center !important;
}

/* Value styles */
.ng-value {
  display: flex !important;
  align-items: center !important;
  visibility: visible !important;
  opacity: 1 !important;
  color: #000000;
  width: 100% !important;
}

.ng-value > div {
  width: 100% !important;
  display: flex !important;
  align-items: center !important;
}

/* Arrow wrapper */
.ng-arrow-wrapper {
  display: flex !important;
  align-items: center !important;
  color: #000000;
  padding-right: 0 !important;
  transition: transform 150ms ease-in-out !important;
  flex-shrink: 0 !important;
}

/* Dark mode styles */
.dark .ng-select-container {
  background-color: #262626;
  border-color: #3f3f46 !important;
  color: #ffffff;
}

.dark .ng-placeholder,
.dark .ng-value {
  color: #ffffff;
}

.dark .ng-arrow-wrapper {
  color: #ffffff;
}

/* Dropdown styles */
.date-picker-dropdown {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  width: 100%;
  min-width: 240px;
  max-width: 280px;
  max-height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
  background-color: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: var(--radius) !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
  z-index: 50;
  opacity: 0;
  transform: translateY(-5px);
  animation: fadeIn 0.15s ease-in-out forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dark mode dropdown */
.dark .date-picker-dropdown {
  background-color: #262626 !important;
  border-color: #3f3f46 !important;
}

/* Body styles */
.date-picker-body {
  padding: 10px;
}

.quick-select-section {
  margin-bottom: 10px;
}

.quick-select-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 4px;
  max-height: 120px;
  overflow-y: auto;
}

.quick-select-button {
  padding: 6px 8px;
  background-color: #f5f5f5;
  border: 1px solid #e5e7eb;
  border-radius: 0.25rem;
  color: #000000;
  font-size: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
}

.quick-select-button:hover {
  background-color: #e5e7eb;
  border-color: #d1d5db;
}

.date-picker-tabs {
  display: flex;
  background-color: #f5f5f5;
  border-radius: 0.25rem 0.25rem 0 0;
  padding: 2px;
  margin: 0;
}

.tab-button {
  flex: 1;
  padding: 6px 8px;
  background: none;
  border: none;
  border-radius: 0.25rem 0.25rem 0 0;
  color: #585757;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
}

.tab-button.active {
  background-color: #ffffff;
  color: #000000;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.date-label {
  display: block;
  margin-bottom: 4px;
  color: #585757;
  font-size: 12px;
  font-weight: 500;
}

.date-input {
  width: 100%;
  height: 30px;
  padding: 0 8px;
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 0.25rem;
  color: #000000;
  font-size: 12px;
  box-sizing: border-box;
}

.date-input:focus {
  outline: none;
  border-color: #000000;
  box-shadow: 0 0 0 1px #ffffff, 0 0 0 2px #000000;
}

/* Range inputs */
.range-inputs {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.range-input-group {
  width: 100%;
}

.single-date-container,
.range-date-container {
  margin-top: 8px;
  width: 100%;
}

/* Shadcn Calendar Styles */
.shadcn-calendar {
  width: 100%;
  background-color: #ffffff;
  border-radius: 0.25rem;
  overflow: hidden;
}

.calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.calendar-title {
  font-size: 14px;
  font-weight: 500;
  color: #000000;
}

.calendar-nav-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 0.25rem;
  color: #000000;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  padding: 0;
  margin: 0 2px;
}

.calendar-nav-button:hover {
  background-color: #e5e7eb;
}

.calendar-grid {
  padding: 8px;
}

.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  margin-bottom: 4px;
}

.weekday {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 24px;
  font-size: 10px;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
}

.calendar-day {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: none;
  border: none;
  border-radius: 0.25rem;
  font-size: 12px;
  color: #000000;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
}

.calendar-day:hover:not(:disabled) {
  background-color: rgba(0, 0, 0, 0.1);
}

.calendar-day.outside-month {
  color: #9ca3af;
}

.calendar-day.today {
  border: 1px solid #000000;
  font-weight: 500;
}

.calendar-day.selected {
  background-color: #000000;
  color: #ffffff;
  font-weight: 500;
}

.calendar-day.selected:hover {
  background-color: #000000;
  opacity: 0.9;
}

.calendar-day.range-start,
.calendar-day.range-end {
  background-color: #000000;
  color: #ffffff;
  font-weight: 500;
}

.calendar-day.range-start:hover,
.calendar-day.range-end:hover {
  background-color: #000000;
  opacity: 0.9;
}

.calendar-day.in-range {
  background-color: rgba(0, 0, 0, 0.1);
}

.calendar-day.in-range:hover {
  background-color: rgba(0, 0, 0, 0.2);
}

.calendar-day:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.date-display {
  padding: 6px 8px;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.25rem;
  font-size: 12px;
  color: #000000;
}

/* Footer styles */
.date-picker-footer {
  display: flex;
  justify-content: flex-end;
  gap: 6px;
  padding: 8px;
  border-top: 1px solid #e5e7eb;
}

.cancel-button {
  padding: 6px 12px;
  background: none;
  border: 1px solid #e5e7eb;
  border-radius: 0.25rem;
  color: #585757;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
}

.cancel-button:hover {
  background-color: #f5f5f5;
}

.apply-button {
  padding: 6px 12px;
  background-color: #000000;
  border: none;
  border-radius: 0.25rem;
  color: white;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
}

.apply-button:hover {
  opacity: 0.9;
}

/* Dark mode styles */
.dark .date-picker-title {
  color: #ffffff;
}

.dark .section-title {
  color: #a1a1aa;
}

.dark .date-picker-header {
  border-color: #3f3f46;
}

.dark .custom-select-section {
  border-color: #3f3f46;
}

.dark .date-picker-tabs {
  background-color: #3f3f46;
}

.dark .tab-button {
  color: #a1a1aa;
}

.dark .tab-button.active {
  background-color: #262626;
  color: #ffffff;
}

.dark .quick-select-button {
  background-color: #3f3f46;
  border-color: #3f3f46;
  color: #ffffff;
}

.dark .quick-select-button app-icon {
  color: #a1a1aa;
}

.dark .quick-select-button:hover {
  background-color: #52525b;
  border-color: #52525b;
}

.dark .date-label {
  color: #a1a1aa;
}

.dark .date-input {
  background-color: #262626;
  border-color: #3f3f46;
  color: #ffffff;
}

.dark .date-input:focus {
  border-color: #ffffff;
  box-shadow: 0 0 0 2px #262626, 0 0 0 4px #ffffff;
}

.dark .calendar-day:hover:not(:disabled) {
  background-color: rgba(255, 255, 255, 0.1);
}

.dark .calendar-day.selected {
  background-color: #ffffff;
  color: #000000;
}

.dark .calendar-day.selected:hover {
  background-color: #ffffff;
  opacity: 0.9;
}

.dark .calendar-day.range-start,
.dark .calendar-day.range-end {
  background-color: #ffffff;
  color: #000000;
}

.dark .calendar-day.range-start:hover,
.dark .calendar-day.range-end:hover {
  background-color: #ffffff;
  opacity: 0.9;
}

.dark .calendar-day.in-range {
  background-color: rgba(255, 255, 255, 0.1);
}

.dark .calendar-day.in-range:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.dark .date-picker-footer {
  border-color: #3f3f46;
}

.dark .cancel-button {
  border-color: #3f3f46;
  color: #a1a1aa;
}

.dark .cancel-button:hover {
  background-color: #3f3f46;
}

.dark .apply-button {
  background-color: #ffffff;
  color: #000000;
}
