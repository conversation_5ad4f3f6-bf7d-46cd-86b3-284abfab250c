/* You can add global styles to this file, and also import other style files */
@import "~@ng-select/ng-select/themes/default.theme.css";

/* Custom CSS Variables for consistent theming */
:root {
  /* Border radius */
  --radius: 0.5rem;

  /* Colors - Using Bootstrap 5 color scheme */
  --background: #ffffff;
  --foreground: #212529;

  /* Card */
  --card: #ffffff;
  --card-foreground: #212529;

  /* Popover */
  --popover: #ffffff;
  --popover-foreground: #212529;

  /* Primary - Using Bootstrap primary */
  --primary: #0d6efd;
  --primary-foreground: #ffffff;

  /* Secondary - Using Bootstrap secondary */
  --secondary: #6c757d;
  --secondary-foreground: #ffffff;

  /* Success - Using Bootstrap success */
  --success: #198754;
  --success-foreground: #ffffff;

  /* Warning - Using Bootstrap warning */
  --warning: #ffc107;
  --warning-foreground: #212529;

  /* Danger/Destructive - Using Bootstrap danger */
  --destructive: #dc3545;
  --destructive-foreground: #ffffff;

  /* Muted - Using <PERSON>tra<PERSON> gray */
  --muted: #f8f9fa;
  --muted-foreground: #6c757d;

  /* Accent - Using <PERSON>trap info */
  --accent: #0dcaf0;
  --accent-foreground: #212529;

  /* Border and Input - Using Bootstrap border colors */
  --border: #dee2e6;
  --input: #dee2e6;

  /* Ring */
  --ring: #0d6efd;

  /* Custom ng-select styles */
  --ng-select-border-color: #dee2e6;
  --ng-select-focus-border-color: #0d6efd;
  --ng-select-highlight-color: #0d6efd;

  /* Shadows */
  --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  --shadow-xl: 0 1.5rem 4rem rgba(0, 0, 0, 0.2);

  /* Custom color variables for compatibility */
  --backgroundPrimary: #ffffff;
  --backgroundSecondary: #f8f9fa;
  --content1: #212529;
  --content2: #6c757d;
  --content3: #adb5bd;
  --blue-2: #e7f1ff;
  --blue-3: #cfe2ff;
  --blue-4: #b6d4fe;
  --blue-5: #9ec5fe;
  --blue-9: #0d6efd;
  --blue-11: #0a58ca;
  --green-3: rgb(233 249 238);
  --green-5: #badbcc;
  --green-11: #146c43;
  --red-2: #f8d7da;
  --error: #dc3545;

  /* Bootstrap color variable overrides */
  --bs-primary: rgb(26, 26, 26);
  --bs-primary-rgb: 26, 26, 26;
  --bs-secondary: rgb(120, 40, 200);
  --bs-secondary-rgb: 120, 40, 200;
  --bs-success: rgb(23, 201, 100);
  --bs-success-rgb: 23, 201, 100;
  --bs-danger: rgb(243, 18, 96);
  --bs-danger-rgb: 243, 18, 96;
  --bs-warning: rgb(245, 165, 36);
  --bs-warning-rgb: 245, 165, 36;
  --bs-info: rgb(0, 145, 255);
  --bs-info-rgb: 0, 145, 255;
  --bs-light: #f8f9fa;
  --bs-dark: #181818;
}

/* Dark mode variables */
.dark {
  /* Colors - Using Bootstrap dark theme */
  --background: #212529;
  --foreground: #f8f9fa;

  /* Card */
  --card: #343a40;
  --card-foreground: #f8f9fa;

  /* Popover */
  --popover: #343a40;
  --popover-foreground: #f8f9fa;

  /* Primary - Using Bootstrap primary for dark mode */
  --primary: #3d8bfd;
  --primary-foreground: #ffffff;

  /* Secondary - Using Bootstrap secondary for dark mode */
  --secondary: #adb5bd;
  --secondary-foreground: #212529;

  /* Success - Using Bootstrap success for dark mode */
  --success: #75b798;
  --success-foreground: #ffffff;

  /* Warning - Using Bootstrap warning for dark mode */
  --warning: #ffca2c;
  --warning-foreground: #212529;

  /* Danger/Destructive - Using Bootstrap danger for dark mode */
  --destructive: #ea868f;
  --destructive-foreground: #ffffff;

  /* Muted - Using Bootstrap gray for dark mode */
  --muted: #495057;
  --muted-foreground: #adb5bd;

  /* Accent - Using Bootstrap info for dark mode */
  --accent: #6edff6;
  --accent-foreground: #212529;

  /* Border and Input - Using Bootstrap border colors for dark mode */
  --border: #495057;
  --input: #495057;

  /* Ring */
  --ring: #3d8bfd;

  /* Custom ng-select styles for dark mode */
  --ng-select-border-color: #495057;
  --ng-select-focus-border-color: #3d8bfd;
  --ng-select-highlight-color: #3d8bfd;

  /* Custom color variables for dark mode compatibility */
  --backgroundPrimary: #212529;
  --backgroundSecondary: #343a40;
  --content1: #f8f9fa;
  --content2: #adb5bd;
  --content3: #6c757d;
  --blue-1: rgb(251 253 255);
  --blue-2: rgb(245 250 255);
  --blue-3: rgb(237 246 255);
  --blue-4: rgb(225 240 255);
  --blue-5: rgb(206 231 254);
  --blue-6: rgb(183 217 248);
  --blue-7: rgb(150 199 242);
  --blue-8: rgb(94 176 239);
  --blue-9: rgb(0 145 255);
  --blue-10: rgb(0 129 241);
  --blue-11: rgb(0 106 220);
  --blue-12: rgb(0 37 77);
  --purple-1: rgb(254 252 254);
  --purple-2: rgb(253 250 255);
  --purple-3: rgb(249 241 254);
  --purple-4: rgb(243 231 252);
  --purple-5: rgb(237 220 249);
  --purple-6: rgb(227 204 244);
  --purple-7: rgb(211 180 237);
  --purple-8: rgb(190 147 228);
  --purple-9: rgb(142 78 198);
  --purple-10: rgb(132 69 188);
  --purple-11: rgb(121 58 175);
  --purple-12: rgb(43 14 68);
  --green-1: rgb(251 254 252);
  --green-2: rgb(242 252 245);
  --green-3: rgb(233 249 238);
  --green-4: rgb(221 243 228);
  --green-5: rgb(204 235 215);
  --green-6: rgb(180 223 196);
  --green-7: rgb(146 206 172);
  --green-8: rgb(91 185 140);
  --green-9: rgb(48 164 108);
  --green-10: rgb(41 151 100);
  --green-11: rgb(24 121 78);
  --green-12: rgb(21 50 38);
  --yellow-1: rgb(253 253 249);
  --yellow-2: rgb(255 252 232);
  --yellow-3: rgb(255 251 209);
  --yellow-4: rgb(255 248 187);
  --yellow-5: rgb(254 242 164);
  --yellow-6: rgb(249 230 140);
  --yellow-7: rgb(239 211 108);
  --yellow-8: rgb(235 188 0);
  --yellow-9: rgb(245 217 10);
  --yellow-10: rgb(247 206 0);
  --yellow-11: rgb(148 104 0);
  --yellow-12: rgb(53 41 15);
  --red-1: rgb(255 252 252);
  --red-2: rgb(255 248 248);
  --red-3: rgb(255 239 239);
  --red-4: rgb(255 229 229);
  --red-5: rgb(253 216 216);
  --red-6: rgb(249 198 198);
  --red-7: rgb(243 174 175);
  --red-8: rgb(235 144 145);
  --red-9: rgb(229 72 77);
  --red-10: rgb(220 61 67);
  --red-11: rgb(205 43 49);
  --red-12: rgb(56 19 22);
  --cyan-1: rgb(250 253 254);
  --cyan-2: rgb(242 252 253);
  --cyan-3: rgb(231 249 251);
  --cyan-4: rgb(216 243 246);
  --cyan-5: rgb(196 234 239);
  --cyan-6: rgb(170 222 230);
  --cyan-7: rgb(132 205 218);
  --cyan-8: rgb(61 185 207);
  --cyan-9: rgb(5 162 194);
  --cyan-10: rgb(8 148 179);
  --cyan-11: rgb(12 119 146);
  --cyan-12: rgb(4 49 60);
  --pink-1: rgb(255 252 254);
  --pink-2: rgb(255 247 252);
  --pink-3: rgb(254 238 248);
  --pink-4: rgb(252 229 243);
  --pink-5: rgb(249 216 236);
  --pink-6: rgb(243 198 226);
  --pink-7: rgb(236 173 212);
  --pink-8: rgb(227 142 195);
  --pink-9: rgb(214 64 159);
  --pink-10: rgb(210 49 151);
  --pink-11: rgb(205 29 141);
  --pink-12: rgb(59 10 42);
  --gray-1: rgb(252 252 252);
  --gray-2: rgb(248 248 248);
  --gray-3: rgb(243 243 243);
  --gray-4: rgb(237 237 237);
  --gray-5: rgb(232 232 232);
  --gray-6: rgb(226 226 226);
  --gray-7: rgb(219 219 219);
  --gray-8: rgb(199 199 199);
  --gray-9: rgb(143 143 143);
  --gray-10: rgb(133 133 133);
  --gray-11: rgb(111 111 111);
  --gray-12: rgb(23 23 23);
  --slate-1: rgb(251 252 253);
  --slate-2: rgb(248 249 250);
  --slate-3: rgb(241 243 245);
  --slate-4: rgb(236 240 240);
  --slate-5: rgb(230 232 235);
  --slate-6: rgb(223 227 230);
  --slate-7: rgb(215 219 223);
  --slate-8: rgb(193 200 205);
  --slate-9: rgb(136 144 150);
  --slate-10: rgb(126 134 140);
  --slate-11: rgb(104 112 118);
  --slate-12: rgb(17 24 28);
  --red-2: #2a1a1b;
  --error: #ea868f;
}

/* Apply light theme by default */
:root {
  color-scheme: light;
}

/* Apply dark theme when dark mode is enabled */
@media (prefers-color-scheme: dark) {
  :root {
    color-scheme: dark;
  }
}

/* Input styles based on Bootstrap */
.input {
  flex-shrink: 0;
  height: 2.5rem;
  width: 100%;
  border-radius: calc(var(--radius) - 2px);
  border: 1px solid var(--border);
  background-color: transparent;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.input::placeholder {
  color: var(--content3);
}

.input:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.btn-outline {
  border-width: 1px;
  border-color: var(--border);
}

.btn {
  border-radius: calc(var(--radius) - 2px);
  height: 2.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out;
}

/* Global ng-select styles */
.ng-select {
  width: 100% !important;
}

.form-control {
  width: 100% !important;
}

.ng-select .ng-select-container {
  border-color: var(--ng-select-border-color) !important;
  border-radius: calc(var(--radius) - 2px) !important;
  min-height: 2.5rem !important;
  font-size: 0.875rem !important;
  height: 2.5rem !important;
  padding-left: 0.5rem !important;
  padding-right: 0.5rem !important;
  display: flex !important;
  align-items: center !important;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
}

.ng-select .ng-value-container {
  padding-left: 0 !important;
  display: flex !important;
  align-items: center !important;
  overflow: visible !important;
}

.ng-select .ng-value {
  display: flex !important;
  align-items: center !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.ng-select .ng-arrow-wrapper {
  padding-right: 0 !important;
  transition: transform 0.15s ease-in-out !important;
}

.ng-select.ng-select-opened .ng-arrow-wrapper {
  transform: rotate(180deg) !important;
}

.ng-select.ng-select-focused .ng-select-container {
  border-color: var(--ng-select-focus-border-color) !important;
  outline: none !important;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25) !important;
}

.ng-select .ng-placeholder {
  color: var(--content2) !important;
  opacity: 1 !important;
  visibility: visible !important;
  align-items: center !important;
}

.ng-dropdown-panel {
  border-radius: var(--radius) !important;
  border-color: var(--ng-select-border-color) !important;
  box-shadow: var(--shadow-md) !important;
  background-color: var(--background) !important;
  /* Add animation for dropdown panel */
  opacity: 0;
  transform: translateY(-5px);
  transition: opacity 0.15s ease-in-out, transform 0.15s ease-in-out;
}

/* Animation for open dropdown */
.ng-dropdown-panel.ng-select-bottom {
  opacity: 1;
  transform: translateY(0);
}

.ng-dropdown-panel.ng-select-top {
  opacity: 1;
  transform: translateY(0);
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option {
  padding: 0.5rem 0.75rem !important;
  font-size: 0.875rem !important;
  color: var(--content1) !important;
  transition: background-color 0.15s ease-in-out, color 0.15s ease-in-out !important;
  position: relative !important;
  overflow: hidden !important;
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected {
  background-color: rgba(13, 110, 253, 0.1) !important;
  color: var(--content1) !important;
  font-weight: 500 !important;
}

/* Prevent highlighting when initially clicking the select */
.ng-select.ng-select-opened
  .ng-dropdown-panel
  .ng-dropdown-panel-items
  .ng-option.ng-option-marked:not(.ng-option-selected):not(:hover) {
  background-color: transparent !important;
}

/* Make hover effect more visible */
.ng-dropdown-panel .ng-dropdown-panel-items .ng-option:hover {
  background-color: var(--muted) !important;
  color: var(--content1) !important;
  cursor: pointer !important;
}

/* Add ripple effect on click */
.ng-dropdown-panel .ng-dropdown-panel-items .ng-option:active::after {
  content: "" !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  width: 100% !important;
  height: 100% !important;
  background-color: rgba(0, 0, 0, 0.05) !important;
  border-radius: 50% !important;
  transform: translate(-50%, -50%) scale(0) !important;
  animation: ripple 300ms ease-out forwards !important;
}

@keyframes ripple {
  to {
    transform: translate(-50%, -50%) scale(2.5) !important;
    opacity: 0 !important;
  }
}

/* Allow highlighting after selection */
.ng-select.ng-select-opened
  .ng-dropdown-panel
  .ng-dropdown-panel-items
  .ng-option:hover {
  background-color: var(--muted) !important;
  color: var(--content1) !important;
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-marked {
  background-color: var(--muted) !important;
  color: var(--content1) !important;
}

/* Dark mode styles for ng-select */
.dark .ng-dropdown-panel {
  background-color: var(--background) !important;
}

.dark .ng-dropdown-panel .ng-dropdown-panel-items .ng-option {
  color: var(--content1) !important;
}

.dark
  .ng-dropdown-panel
  .ng-dropdown-panel-items
  .ng-option.ng-option-selected {
  background-color: rgba(61, 139, 253, 0.2) !important;
  color: var(--content1) !important;
}

/* Dark mode hover and marked styles */
.dark .ng-dropdown-panel .ng-dropdown-panel-items .ng-option:hover {
  background-color: var(--muted) !important;
  color: var(--content1) !important;
  cursor: pointer !important;
}

/* Dark mode ripple effect */
.dark .ng-dropdown-panel .ng-dropdown-panel-items .ng-option:active::after {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.dark
  .ng-select.ng-select-opened
  .ng-dropdown-panel
  .ng-dropdown-panel-items
  .ng-option:hover {
  background-color: var(--muted) !important;
  color: var(--content1) !important;
}

.dark .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-marked {
  background-color: var(--muted) !important;
  color: var(--content1) !important;
}

/* Utility classes for compatibility */
.badge-group {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 0.375rem;
  border: 1px solid transparent;
}

.badge-flat {
  display: inline-block;
  padding: 0.25em 0.9em;
  font-size: 0.75em;
  font-weight: 400;
  border-radius: 999px;
  border: none;
  background: #e9ecef;
  color: #333;
  line-height: 1.2;
}

/* Primary */
.badge-flat-primary {
  background: #e3f0fc;
  color: #1769aa;
}

/* Secondary */
.badge-flat-secondary {
  background: var(--purple-3);
  color: var(--purple-11);
}

/* Success */
.badge-flat-success {
  background: var(--green-3);
  color: #218838;
}

/* Danger */
.badge-flat-danger {
  background: #fbeaea;
  color: #c82333;
}

/* Warning */
.badge-flat-warning {
  background: #fff7d6;
  color: #b8860b;
}

/* Info */
.badge-flat-info {
  background: #e6f7fa;
  color: #138496;
}

/* Dark */
.badge-flat-dark {
  background: #e6e6e6;
  color: #343a40;
}

/* Light */
.badge-flat-light {
  background: #f8f9fa;
  color: #6c757d;
}

/* Bootstrap utility classes for compatibility */
.card {
  background-color: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  box-shadow: none !important;
}

.card-body {
  padding: 1.5rem;
}

.card-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border);
  font-weight: 600;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .max-md-max-w-full {
    max-width: 100% !important;
  }
}

/* Line clamp utility */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Loading spinner */
.loading {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid currentColor;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

.loading-spinner {
  border-width: 2px;
}

.loading-lg {
  width: 2rem;
  height: 2rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Alert styles */
.alert {
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: var(--radius);
}

.alert-error {
  color: var(--destructive-foreground);
  background-color: var(--destructive);
  border-color: var(--destructive);
}

.alert-success {
  color: var(--success-foreground);
  background-color: var(--success);
  border-color: var(--success);
}

/* Form styles */
.form-group {
  margin-bottom: 1rem;
}

.label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--content1);
}

.label-text {
  font-size: 0.875rem;
}

/* Button styles */
.btn-primary {
  background-color: var(--bs-primary) !important;
  border-color: var(--bs-primary) !important;
  color: #fff !important;
}

.btn-primary:hover,
.btn-primary:focus {
  background-color: #222 !important;
  border-color: #222 !important;
  color: #fff !important;
}

.btn-secondary {
  background-color: var(--bs-secondary) !important;
  border-color: var(--bs-secondary) !important;
  color: #fff !important;
}
.btn-secondary:hover,
.btn-secondary:focus {
  background-color: rgb(100, 30, 170) !important;
  border-color: rgb(100, 30, 170) !important;
  color: #fff !important;
}

.btn-danger {
  background-color: var(--destructive);
  border-color: var(--destructive);
  color: var(--destructive-foreground);
}

.btn-danger:hover {
  background-color: var(--destructive);
  border-color: var(--destructive);
  opacity: 0.9;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
}

/* Text utilities */
.text-primary {
  color: var(--bs-primary) !important;
}

.text-secondary {
  color: var(--bs-secondary) !important;
}

.text-success {
  color: var(--bs-success) !important;
}

.text-warning {
  color: var(--bs-warning) !important;
}

.text-danger {
  color: var(--bs-danger) !important;
}

.text-content1 {
  color: var(--content1) !important;
}

.text-content2 {
  color: var(--content2) !important;
}

.text-content3 {
  color: var(--content3) !important;
}

/* Background utilities */
.bg-backgroundPrimary {
  background-color: rgb(255, 255, 255) !important;
}

.bg-backgroundSecondary {
  background-color: rgb(245, 245, 245) !important;
}

.bg-blue-2 {
  background-color: var(--blue-2) !important;
}

.bg-blue-3 {
  background-color: var(--blue-3) !important;
}

.bg-blue-4 {
  background-color: var(--blue-4) !important;
}

.bg-blue-5 {
  background-color: var(--blue-5) !important;
}

.bg-blue-9 {
  background-color: var(--blue-9) !important;
}

.bg-blue-11 {
  background-color: var(--blue-11) !important;
}

.bg-green-3 {
  background-color: var(--green-3) !important;
}

.bg-green-5 {
  background-color: var(--green-5) !important;
}

.bg-green-11 {
  background-color: var(--green-11) !important;
}

.bg-red-2 {
  background-color: var(--red-2) !important;
}

.bg-success {
  background-color: var(--success) !important;
}

.bg-error {
  background-color: var(--error) !important;
}

/* Border utilities */
.border-border {
  border-color: var(--border) !important;
}

.border-blue-5 {
  border-color: var(--blue-5) !important;
}

.border-green-5 {
  border-color: var(--green-5) !important;
}

/* Shadow utilities */
.shadow-sm {
  box-shadow: var(--shadow-sm) !important;
}

.shadow-md {
  box-shadow: var(--shadow-md) !important;
}

.shadow-lg {
  box-shadow: var(--shadow-lg) !important;
}

.shadow-xl {
  box-shadow: var(--shadow-xl) !important;
}

/* Hover utilities */
.hover-shadow-sm:hover {
  box-shadow: var(--shadow-sm) !important;
}

.hover-shadow-md:hover {
  box-shadow: var(--shadow-md) !important;
}

.hover-shadow-lg:hover {
  box-shadow: var(--shadow-lg) !important;
}

.hover-bg-blue-2:hover {
  background-color: var(--blue-2) !important;
}

.hover-bg-blue-3:hover {
  background-color: var(--blue-3) !important;
}

.hover-bg-blue-4:hover {
  background-color: var(--blue-4) !important;
}

.hover-bg-blue-5:hover {
  background-color: var(--blue-5) !important;
}

.hover-bg-green-4:hover {
  background-color: var(--green-4) !important;
}

.hover-bg-backgroundSecondary:hover {
  background-color: var(--backgroundSecondary) !important;
}

.hover-text-blue-9:hover {
  color: var(--blue-9) !important;
}

.hover-text-blue-11:hover {
  color: var(--blue-11) !important;
}

.hover-text-content1:hover {
  color: var(--content1) !important;
}

.hover-text-error:hover {
  color: var(--error) !important;
}

.hover-border-blue-5:hover {
  border-color: var(--blue-5) !important;
}

/* Transition utilities */
.transition-all {
  transition: all 0.15s ease-in-out !important;
}

.transition-colors {
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out !important;
}

.transition-opacity {
  transition: opacity 0.15s ease-in-out !important;
}

.transition-shadow {
  transition: box-shadow 0.15s ease-in-out !important;
}

.transition-transform {
  transition: transform 0.15s ease-in-out !important;
}

/* Duration utilities */
.duration-200 {
  transition-duration: 0.2s !important;
}

.duration-300 {
  transition-duration: 0.3s !important;
}

/* Transform utilities */
.hover-scale-110:hover {
  transform: scale(1.1) !important;
}

/* Opacity utilities */
.opacity-0 {
  opacity: 0 !important;
}

.opacity-80 {
  opacity: 0.8 !important;
}

.hover-opacity-10:hover {
  opacity: 0.1 !important;
}

/* Position utilities */
.absolute {
  position: absolute !important;
}

.relative {
  position: relative !important;
}

.sticky {
  position: sticky !important;
}

.top-4 {
  top: 1rem !important;
}

.inset-0 {
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
}

/* Display utilities */
.flex {
  display: flex !important;
}

.inline-flex {
  display: inline-flex !important;
}

.grid {
  display: grid !important;
}

.block {
  display: block !important;
}

.inline-block {
  display: inline-block !important;
}

.hidden {
  display: none !important;
}

/* Flex utilities */
.flex-col {
  flex-direction: column !important;
}

.flex-row {
  flex-direction: row !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-1 {
  flex: 1 1 0% !important;
}

.flex-shrink-0 {
  flex-shrink: 0 !important;
}

.flex-none {
  flex: none !important;
}

/* Justify content utilities */
.justify-center {
  justify-content: center !important;
}

.justify-between {
  justify-content: space-between !important;
}

.justify-end {
  justify-content: flex-end !important;
}

/* Align items utilities */
.items-center {
  align-items: center !important;
}

.items-end {
  align-items: flex-end !important;
}

.items-start {
  align-items: flex-start !important;
}

/* Gap utilities */
.gap-1 {
  gap: 0.25rem !important;
}

.gap-2 {
  gap: 0.5rem !important;
}

.gap-3 {
  gap: 0.75rem !important;
}

.gap-4 {
  gap: 1rem !important;
}

.gap-5 {
  gap: 1.25rem !important;
}

.gap-6 {
  gap: 1.5rem !important;
}

/* Width and height utilities */
.w-full {
  width: 100% !important;
}

.w-16 {
  width: 4rem !important;
}

.w-24 {
  width: 6rem !important;
}

.w-28 {
  width: 7rem !important;
}

.w-96 {
  width: 24rem !important;
}

.h-16 {
  height: 4rem !important;
}

.h-24 {
  height: 6rem !important;
}

.h-28 {
  height: 7rem !important;
}

.h-full {
  height: 100% !important;
}

.min-h-screen {
  min-height: 100vh !important;
}

/* Padding utilities */
.p-1 {
  padding: 0.25rem !important;
}

.p-2 {
  padding: 0.5rem !important;
}

.p-3 {
  padding: 0.75rem !important;
}

.p-4 {
  padding: 1rem !important;
}

.p-5 {
  padding: 1.25rem !important;
}

.p-6 {
  padding: 1.5rem !important;
}

.px-2 {
  padding-left: 0.5rem !important;
  padding-right: 0.5rem !important;
}

.px-3 {
  padding-left: 0.75rem !important;
  padding-right: 0.75rem !important;
}

.px-4 {
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

.px-5 {
  padding-left: 1.25rem !important;
  padding-right: 1.25rem !important;
}

.px-6 {
  padding-left: 1.5rem !important;
  padding-right: 1.5rem !important;
}

.py-1 {
  padding-top: 0.25rem !important;
  padding-bottom: 0.25rem !important;
}

.py-2 {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}

.py-4 {
  padding-top: 1rem !important;
  padding-bottom: 1rem !important;
}

.py-6 {
  padding-top: 1.5rem !important;
  padding-bottom: 1.5rem !important;
}

.py-20 {
  padding-top: 5rem !important;
  padding-bottom: 5rem !important;
}

.pl-6 {
  padding-left: 1.5rem !important;
}

.pr-2 {
  padding-right: 0.5rem !important;
}

.pt-4 {
  padding-top: 1rem !important;
}

.pb-2 {
  padding-bottom: 0.5rem !important;
}

/* Margin utilities */
.m-0 {
  margin: 0 !important;
}

.mb-1 {
  margin-bottom: 0.25rem !important;
}

.mb-2 {
  margin-bottom: 0.5rem !important;
}

.mb-3 {
  margin-bottom: 0.75rem !important;
}

.mb-4 {
  margin-bottom: 1rem !important;
}

.mb-6 {
  margin-bottom: 1.5rem !important;
}

.mb-8 {
  margin-bottom: 2rem !important;
}

.ml-7 {
  margin-left: 1.75rem !important;
}

.mr-1 {
  margin-right: 0.25rem !important;
}

.mr-2 {
  margin-right: 0.5rem !important;
}

.mt-1 {
  margin-top: 0.25rem !important;
}

.mt-2 {
  margin-top: 0.5rem !important;
}

.mt-4 {
  margin-top: 1rem !important;
}

.mt-6 {
  margin-top: 1.5rem !important;
}

/* Border radius utilities */
.rounded {
  border-radius: 0.25rem !important;
}

.rounded-md {
  border-radius: 0.375rem !important;
}

.rounded-lg {
  border-radius: 0.5rem !important;
}

.rounded-full {
  border-radius: 9999px !important;
}

/* Border utilities */
.border {
  border-width: 1px !important;
}

.border-b {
  border-bottom-width: 1px !important;
}

.border-t {
  border-top-width: 1px !important;
}

/* Text utilities */
.text-xs {
  font-size: 0.75rem !important;
}

.text-sm {
  font-size: 0.875rem !important;
}

.text-base {
  font-size: 1rem !important;
}

.text-lg {
  font-size: 1.125rem !important;
}

.text-xl {
  font-size: 1.25rem !important;
}

.text-2xl {
  font-size: 1.5rem !important;
}

.text-4xl {
  font-size: 2.25rem !important;
}

.font-medium {
  font-weight: 500 !important;
}

.font-semibold {
  font-weight: 600 !important;
}

.font-bold {
  font-weight: 700 !important;
}

.text-center {
  text-align: center !important;
}

.text-right {
  text-align: right !important;
}

/* Cursor utilities */
.cursor-pointer {
  cursor: pointer !important;
}

.cursor-default {
  cursor: default !important;
}

/* Overflow utilities */
.overflow-hidden {
  overflow: hidden !important;
}

.overflow-visible {
  overflow: visible !important;
}

/* Truncate utility */
.truncate {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

/* Max width utilities */
.max-w-full {
  max-width: 100% !important;
}

.max-w-120px {
  max-width: 120px !important;
}

.max-w-100px {
  max-width: 100px !important;
}

/* Space utilities */
.space-x-5 > * + * {
  margin-left: 1.25rem !important;
}

/* Last child utilities */
.last-border-0:last-child {
  border: 0 !important;
}

.last-pb-0:last-child {
  padding-bottom: 0 !important;
}

/* Negative margin utilities */
.-mx-2 {
  margin-left: -0.5rem !important;
  margin-right: -0.5rem !important;
}

/* Ring utilities */
.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
    var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
    calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
    var(--tw-shadow, 0 0 #0000);
}

.ring-border {
  --tw-ring-color: var(--border);
}

.group-hover-ring-blue-5.group:hover {
  --tw-ring-color: var(--blue-5);
}

/* Group hover utilities */
.group:hover .group-hover-text-blue-9 {
  color: var(--blue-9) !important;
}

.group:hover .group-hover-text-blue-11 {
  color: var(--blue-11) !important;
}

.group:hover .group-hover-text-content1 {
  color: var(--content1) !important;
}

.group:hover .group-hover-ring-blue-5 {
  --tw-ring-color: var(--blue-5);
}

/* Responsive grid utilities */
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
}

.md-grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
}

.md-col-span-2 {
  grid-column: span 2 / span 2 !important;
}

/* Responsive utilities */
@media (min-width: 768px) {
  .md-grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
  }

  .md-col-span-2 {
    grid-column: span 2 / span 2 !important;
  }
}

/* --- Custom Bootstrap and UI Color Fixes --- */

.badge.bg-primary {
  background-color: var(--bs-primary) !important;
  color: #fff !important;
}
.badge.bg-secondary {
  background-color: var(--bs-secondary) !important;
  color: #fff !important;
}
.badge.bg-success {
  background-color: var(--bs-success) !important;
  color: #fff !important;
}
.badge.bg-warning {
  background-color: var(--bs-warning) !important;
  color: #fff !important;
}
.badge.bg-danger {
  background-color: var(--bs-danger) !important;
  color: #fff !important;
}

.avatar-bg {
  background-color: var(--blue-3) !important;
  color: var(--bs-primary) !important;
  font-weight: bold;
}

.card:hover,
.hover-shadow:hover {
  box-shadow: 0 0.5rem 1.5rem rgba(26, 26, 26, 0.1) !important;
}

.text-primary {
  color: var(--bs-primary) !important;
}
.text-secondary {
  color: var(--bs-secondary) !important;
}
.text-success {
  color: var(--bs-success) !important;
}
.text-warning {
  color: var(--bs-warning) !important;
}
.text-danger {
  color: var(--bs-danger) !important;
}

.bg-backgroundPrimary {
  background-color: rgb(255, 255, 255) !important;
}
.bg-backgroundSecondary {
  background-color: rgb(245, 245, 245) !important;
}

/* Active card style button */
.btn.btn-primary.btn-sm.active,
.btn.btn-primary.btn-sm:active {
  background-color: var(--bs-primary) !important;
  color: #fff !important;
  border-color: var(--bs-primary) !important;
}

/* ng-select and input focus: black border, not blue */
.ng-select.ng-select-focused .ng-select-container {
  border-color: var(--bs-primary) !important;
  box-shadow: 0 0 0 0.2rem rgba(26, 26, 26, 0.15) !important;
}

.input:focus,
.form-control:focus {
  border-color: var(--bs-primary) !important;
  box-shadow: 0 0 0 0.2rem rgba(26, 26, 26, 0.15) !important;
}
