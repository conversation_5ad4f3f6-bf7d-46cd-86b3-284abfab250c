import { Component, OnInit } from '@angular/core';
import { ProfileService } from '../../../core/services/profile/profile.service';
import { ProfileData } from '../../../core/models/profile.model';
import { IconComponent } from 'src/app/shared/components/icon/icon.component';

interface SelectOption {
  label: string;
  value: string;
}

@Component({
  selector: 'app-profile-list',
  templateUrl: './profile-list.component.html',
  styleUrls: ['./profile-list.component.css'],
})
export class ProfileListComponent implements OnInit {
  profiles: ProfileData[] = [];
  loading = true;
  error = false;
  profileListStyle: 'style1' | 'style2' = 'style1';

  // Search filters
  searchQuery = '';
  experienceLevel: string | null = null;
  location: string | null = null;
  language: string | null = null;
  availability: string | null = null;
  availabilityDate: string | null = null;

  // Date picker properties
  datePickerInitialSelection: any = null;

  // Options for ng-select dropdowns
  experienceLevelOptions: SelectOption[] = [
    { label: 'Any', value: '' },
    { label: 'Junior (1-3 years)', value: 'Junior (1-3 years)' },
    { label: 'Mid-level (4-6 years)', value: 'Mid-level (4-6 years)' },
    { label: 'Senior (7-10 years)', value: 'Senior (7-10 years)' },
    { label: 'Partner (10+ years)', value: 'Partner (10+ years)' },
  ];

  locationOptions: SelectOption[] = [
    { label: 'Any', value: '' },
    { label: 'New York', value: 'New York' },
    { label: 'San Francisco', value: 'San Francisco' },
    { label: 'Chicago', value: 'Chicago' },
    { label: 'Los Angeles', value: 'Los Angeles' },
  ];

  languageOptions: SelectOption[] = [
    { label: 'Any', value: '' },
    { label: 'English', value: 'English' },
    { label: 'Spanish', value: 'Spanish' },
    { label: 'Mandarin', value: 'Mandarin' },
    { label: 'French', value: 'French' },
  ];

  availabilityOptions: SelectOption[] = [
    { label: 'Any', value: '' },
    { label: 'Available now', value: 'Available now' },
    { label: 'Available next week', value: 'Available next week' },
    { label: 'Available next month', value: 'Available next month' },
  ];

  constructor(private profileService: ProfileService) {}

  ngOnInit(): void {
    this.loadProfiles();

    // Initialize date picker if availability is already set
    if (this.availability) {
      const today = new Date();

      switch (this.availability) {
        case 'Available now':
          this.availabilityDate = today.toISOString().split('T')[0];
          this.datePickerInitialSelection = {
            type: 'single',
            singleDate: this.availabilityDate,
          };
          break;
        case 'Available next week':
          const nextWeek = new Date();
          nextWeek.setDate(today.getDate() + 7);
          this.availabilityDate = nextWeek.toISOString().split('T')[0];
          this.datePickerInitialSelection = {
            type: 'single',
            singleDate: this.availabilityDate,
          };
          break;
        case 'Available next month':
          const nextMonth = new Date();
          nextMonth.setDate(today.getDate() + 30);
          this.availabilityDate = nextMonth.toISOString().split('T')[0];
          this.datePickerInitialSelection = {
            type: 'single',
            singleDate: this.availabilityDate,
          };
          break;
        default:
          this.availabilityDate = null;
          this.datePickerInitialSelection = {
            type: 'single',
            singleDate: null,
          };
      }
    } else {
      this.datePickerInitialSelection = {
        type: 'single',
        singleDate: null,
      };
    }
  }

  loadProfiles(): void {
    this.profileService.getAllProfiles().subscribe({
      next: (data) => {
        this.profiles = data;
        this.loading = false;
      },
      error: (err) => {
        console.error('Error loading profiles', err);
        this.error = true;
        this.loading = false;
      },
    });
  }

  searchProfiles(): void {
    this.loading = true;
    this.error = false;

    this.profileService
      .searchProfiles(
        this.searchQuery,
        this.experienceLevel || '',
        this.location || '',
        this.language || '',
        this.availability || ''
      )
      .subscribe({
        next: (data) => {
          this.profiles = data;
          this.loading = false;
        },
        error: (err) => {
          console.error('Error searching profiles', err);
          this.error = true;
          this.loading = false;
        },
      });
  }

  // The setter methods are no longer needed as we're using two-way binding with [(ngModel)]

  setProfileListStyle(style: 'style1' | 'style2'): void {
    this.profileListStyle = style;
  }

  clearFilters(): void {
    // Reset all search filters to default values
    this.searchQuery = '';
    this.experienceLevel = null;
    this.location = null;
    this.language = null;
    this.availability = null;
    this.availabilityDate = null;

    // Reset date picker
    this.datePickerInitialSelection = {
      type: 'single',
      singleDate: null,
    };

    // Load all profiles (unfiltered)
    this.loadProfiles();
  }

  hasFiltersApplied(): boolean {
    // Check if any search filters are applied
    return !!(
      this.searchQuery ||
      this.experienceLevel ||
      this.location ||
      this.language ||
      this.availability ||
      this.availabilityDate
    );
  }

  onDateSelectionChange(selection: any): void {
    if (selection.type === 'single' && selection.singleDate) {
      this.availabilityDate = selection.singleDate;

      const date = new Date(selection.singleDate);
      const today = new Date();
      const nextWeek = new Date();
      nextWeek.setDate(today.getDate() + 7);
      const nextMonth = new Date();
      nextMonth.setDate(today.getDate() + 30);

      // Set availability based on selected date
      if (date <= today) {
        this.availability = 'Available now';
      } else if (date <= nextWeek) {
        this.availability = 'Available next week';
      } else if (date <= nextMonth) {
        this.availability = 'Available next month';
      } else {
        this.availability = ''; // Any
      }
    } else if (
      selection.type === 'range' &&
      selection.startDate &&
      selection.endDate
    ) {
      // For date range, we'll use the start date to determine availability
      this.availabilityDate = selection.startDate;

      const date = new Date(selection.startDate);
      const today = new Date();
      const nextWeek = new Date();
      nextWeek.setDate(today.getDate() + 7);
      const nextMonth = new Date();
      nextMonth.setDate(today.getDate() + 30);

      // Set availability based on selected date
      if (date <= today) {
        this.availability = 'Available now';
      } else if (date <= nextWeek) {
        this.availability = 'Available next week';
      } else if (date <= nextMonth) {
        this.availability = 'Available next month';
      } else {
        this.availability = ''; // Any
      }
    } else {
      this.availabilityDate = null;
      this.availability = null;
    }
  }
}
