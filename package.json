{"name": "skill", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "engines": {"node": "20.x"}, "private": true, "dependencies": {"@angular/animations": "^16.2.0", "@angular/cdk": "16.2.14", "@angular/common": "^16.2.0", "@angular/compiler": "^16.2.0", "@angular/core": "^16.2.0", "@angular/forms": "^16.2.0", "@angular/material": "16.2.14", "@angular/material-moment-adapter": "16.2.14", "@angular/platform-browser": "^16.2.0", "@angular/platform-browser-dynamic": "^16.2.0", "@angular/router": "^16.2.0", "@angular/service-worker": "^16.2.0", "@azure/msal-angular": "^3.0.8", "@azure/msal-browser": "^3.5.0", "@ng-bootstrap/ng-bootstrap": "^15.1.2", "@ng-select/ng-select": "11", "@popperjs/core": "^2.11.6", "@tabler/icons": "^3.31.0", "bootstrap": "^5.3.2", "bootstrap-icons": "^1.11.2", "chart.js": "^4.4.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-angular": "^0.507.0", "moment": "^2.29.4", "ng2-charts": "^5.0.3", "ngx-slider-v2": "^17.0.0", "rxjs": "~7.8.0", "tslib": "^2.6.3", "uuid4": "^2.0.3", "xlsx": "^0.18.5", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "16.2.12", "@angular-eslint/builder": "16.3.1", "@angular-eslint/eslint-plugin": "16.3.1", "@angular-eslint/eslint-plugin-template": "16.3.1", "@angular-eslint/schematics": "^18.4.0", "@angular/cli": "16.2.12", "@angular/compiler-cli": "16.2.12", "@angular/localize": "^16.2.0", "@types/html2canvas": "^1.0.0", "@types/jasmine": "~4.3.0", "@typescript-eslint/eslint-plugin": "5.62.0", "@typescript-eslint/parser": "5.62.0", "eslint": "^8.51.0", "typescript": "~5.1.3"}, "resolutions": {"string-width": "4.1.0", "strip-ansi": "6.0.0", "wrap-ansi": "7.0.0"}}